"""
RISC (Risk and Incident Sharing and Communications) Service Implementation.
Handles Google security events and automated threat responses.
"""

import os
import json
import hmac
import hashlib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from enum import Enum
from pydantic import BaseModel, Field
from fastapi import HTTPException, status
import jwt

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RISCEventType(str, Enum):
    """RISC security event types."""
    ACCOUNT_TAKEOVER = "https://schemas.openid.net/secevent/risc/event-type/account-takeover"
    SUSPICIOUS_ACTIVITY = "https://schemas.openid.net/secevent/risc/event-type/suspicious-activity"
    CREDENTIAL_COMPROMISE = "https://schemas.openid.net/secevent/risc/event-type/credential-compromise"
    SESSION_REVOCATION = "https://schemas.openid.net/secevent/risc/event-type/session-revocation"
    ACCOUNT_DISABLED = "https://schemas.openid.net/secevent/risc/event-type/account-disabled"
    ACCOUNT_ENABLED = "https://schemas.openid.net/secevent/risc/event-type/account-enabled"


class RISCEventSeverity(str, Enum):
    """RISC event severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RISCSubject(BaseModel):
    """RISC event subject information."""
    subject_type: str = Field(..., description="Type of subject (email, phone, etc.)")
    email: Optional[str] = Field(None, description="User email address")
    phone_number: Optional[str] = Field(None, description="User phone number")
    issuer: Optional[str] = Field(None, description="Token issuer")
    subject: Optional[str] = Field(None, description="Subject identifier")


class RISCEventData(BaseModel):
    """RISC security event data."""
    event_type: RISCEventType = Field(..., description="Type of security event")
    severity: RISCEventSeverity = Field(default=RISCEventSeverity.MEDIUM, description="Event severity")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Event timestamp")
    subject: RISCSubject = Field(..., description="Event subject")
    event_data: Dict[str, Any] = Field(default_factory=dict, description="Additional event data")
    reason: Optional[str] = Field(None, description="Reason for the event")
    initiating_entity: Optional[str] = Field(None, description="Entity that initiated the event")


class RISCResponse(BaseModel):
    """RISC event response."""
    event_id: str = Field(..., description="Unique event identifier")
    processed: bool = Field(..., description="Whether event was processed")
    actions_taken: List[str] = Field(default_factory=list, description="Actions taken in response")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")
    error: Optional[str] = Field(None, description="Error message if processing failed")


class RISCService:
    """RISC service for handling Google security events."""

    def __init__(self):
        self.webhook_secret = os.environ.get("RISC_WEBHOOK_SECRET", "")
        self.enable_logging = os.environ.get("RISC_ENABLE_LOGGING", "true").lower() == "true"
        self.auto_revoke_compromised = os.environ.get("RISC_AUTO_REVOKE_COMPROMISED", "true").lower() == "true"
        
        if not self.webhook_secret:
            logger.warning("RISC_WEBHOOK_SECRET not configured - webhook signature verification disabled")

    def verify_webhook_signature(self, payload: bytes, signature: str) -> bool:
        """
        Verify RISC webhook signature from Google.
        
        Args:
            payload: Raw webhook payload
            signature: Signature from Google
            
        Returns:
            True if signature is valid, False otherwise
        """
        if not self.webhook_secret:
            logger.warning("Webhook signature verification skipped - no secret configured")
            return True
            
        try:
            # Google uses HMAC-SHA256 for webhook signatures
            expected_signature = hmac.new(
                self.webhook_secret.encode('utf-8'),
                payload,
                hashlib.sha256
            ).hexdigest()
            
            # Compare signatures securely
            return hmac.compare_digest(f"sha256={expected_signature}", signature)
            
        except Exception as e:
            logger.error(f"Error verifying webhook signature: {e}")
            return False

    def parse_risc_event(self, payload: Dict[str, Any]) -> RISCEventData:
        """
        Parse RISC security event from Google.
        
        Args:
            payload: RISC event payload
            
        Returns:
            Parsed RISC event data
            
        Raises:
            HTTPException: If event parsing fails
        """
        try:
            # Extract event information
            events = payload.get("events", {})
            if not events:
                raise ValueError("No events found in payload")
            
            # Get the first event (RISC typically sends one event per webhook)
            event_type = list(events.keys())[0]
            event_data = events[event_type]
            
            # Extract subject information
            subject_data = payload.get("sub", {})
            if isinstance(subject_data, str):
                # Subject is a string (user ID)
                subject = RISCSubject(
                    subject_type="user_id",
                    subject=subject_data
                )
            else:
                # Subject is an object with detailed information
                subject = RISCSubject(
                    subject_type=subject_data.get("subject_type", "email"),
                    email=subject_data.get("email"),
                    phone_number=subject_data.get("phone_number"),
                    issuer=subject_data.get("iss"),
                    subject=subject_data.get("sub")
                )
            
            # Determine severity based on event type
            severity = self._determine_event_severity(event_type)
            
            # Create RISC event
            risc_event = RISCEventData(
                event_type=event_type,
                severity=severity,
                subject=subject,
                event_data=event_data,
                reason=event_data.get("reason"),
                initiating_entity=event_data.get("initiating_entity")
            )
            
            return risc_event
            
        except Exception as e:
            logger.error(f"Error parsing RISC event: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid RISC event format: {str(e)}"
            )

    def _determine_event_severity(self, event_type: str) -> RISCEventSeverity:
        """Determine event severity based on type."""
        severity_mapping = {
            RISCEventType.ACCOUNT_TAKEOVER: RISCEventSeverity.CRITICAL,
            RISCEventType.CREDENTIAL_COMPROMISE: RISCEventSeverity.HIGH,
            RISCEventType.ACCOUNT_DISABLED: RISCEventSeverity.HIGH,
            RISCEventType.SESSION_REVOCATION: RISCEventSeverity.MEDIUM,
            RISCEventType.SUSPICIOUS_ACTIVITY: RISCEventSeverity.MEDIUM,
            RISCEventType.ACCOUNT_ENABLED: RISCEventSeverity.LOW,
        }
        return severity_mapping.get(event_type, RISCEventSeverity.MEDIUM)

    async def process_security_event(
        self, 
        event: RISCEventData,
        user_service,  # Injected dependency
        oauth_user_service  # Injected dependency
    ) -> RISCResponse:
        """
        Process RISC security event and take appropriate actions.
        
        Args:
            event: RISC security event
            user_service: User service for user operations
            oauth_user_service: OAuth user service for token operations
            
        Returns:
            RISC response with actions taken
        """
        event_id = f"risc_{int(datetime.utcnow().timestamp())}_{event.event_type.split('/')[-1]}"
        actions_taken = []
        
        try:
            # Log the security event
            if self.enable_logging:
                logger.info(f"Processing RISC event {event_id}: {event.event_type} for {event.subject.email}")
            
            # Find user by email or subject
            user = await self._find_user_by_subject(event.subject, user_service)
            if not user:
                logger.warning(f"User not found for RISC event: {event.subject.email or event.subject.subject}")
                return RISCResponse(
                    event_id=event_id,
                    processed=False,
                    error="User not found"
                )
            
            # Process event based on type
            if event.event_type == RISCEventType.ACCOUNT_TAKEOVER:
                actions_taken.extend(await self._handle_account_takeover(user, oauth_user_service))
                
            elif event.event_type == RISCEventType.CREDENTIAL_COMPROMISE:
                actions_taken.extend(await self._handle_credential_compromise(user, oauth_user_service))
                
            elif event.event_type == RISCEventType.SESSION_REVOCATION:
                actions_taken.extend(await self._handle_session_revocation(user, oauth_user_service))
                
            elif event.event_type == RISCEventType.ACCOUNT_DISABLED:
                actions_taken.extend(await self._handle_account_disabled(user, user_service))
                
            elif event.event_type == RISCEventType.SUSPICIOUS_ACTIVITY:
                actions_taken.extend(await self._handle_suspicious_activity(user, event))
                
            elif event.event_type == RISCEventType.ACCOUNT_ENABLED:
                actions_taken.extend(await self._handle_account_enabled(user, user_service))
            
            # Log actions taken
            if self.enable_logging and actions_taken:
                logger.info(f"RISC event {event_id} processed. Actions: {', '.join(actions_taken)}")
            
            return RISCResponse(
                event_id=event_id,
                processed=True,
                actions_taken=actions_taken
            )
            
        except Exception as e:
            logger.error(f"Error processing RISC event {event_id}: {e}")
            return RISCResponse(
                event_id=event_id,
                processed=False,
                error=str(e)
            )

    async def _find_user_by_subject(self, subject: RISCSubject, user_service) -> Optional[Dict]:
        """Find user by RISC subject information."""
        try:
            if subject.email:
                return await user_service.get_user_by_email(subject.email)
            elif subject.subject:
                return await user_service.get_user_by_oauth_subject(subject.subject)
            return None
        except Exception:
            return None

    async def _handle_account_takeover(self, user: Dict, oauth_user_service) -> List[str]:
        """Handle account takeover event."""
        actions = []
        
        if self.auto_revoke_compromised:
            # Revoke all OAuth tokens
            await oauth_user_service.revoke_all_user_tokens(user["_id"])
            actions.append("revoked_all_oauth_tokens")
            
            # Revoke all API keys
            await oauth_user_service.revoke_all_user_api_keys(user["_id"])
            actions.append("revoked_all_api_keys")
            
            # Disable user account temporarily
            await oauth_user_service.disable_user_account(user["_id"], reason="account_takeover")
            actions.append("disabled_user_account")
        
        actions.append("logged_security_event")
        return actions

    async def _handle_credential_compromise(self, user: Dict, oauth_user_service) -> List[str]:
        """Handle credential compromise event."""
        actions = []
        
        if self.auto_revoke_compromised:
            # Revoke OAuth tokens
            await oauth_user_service.revoke_all_user_tokens(user["_id"])
            actions.append("revoked_oauth_tokens")
        
        actions.append("logged_security_event")
        return actions

    async def _handle_session_revocation(self, user: Dict, oauth_user_service) -> List[str]:
        """Handle session revocation event."""
        actions = []
        
        # Revoke specific session or all sessions
        await oauth_user_service.revoke_user_sessions(user["_id"])
        actions.append("revoked_user_sessions")
        
        return actions

    async def _handle_account_disabled(self, user: Dict, user_service) -> List[str]:
        """Handle account disabled event."""
        actions = []
        
        # Disable user account
        await user_service.disable_user(user["_id"], reason="google_account_disabled")
        actions.append("disabled_user_account")
        
        return actions

    async def _handle_suspicious_activity(self, user: Dict, event: RISCEventData) -> List[str]:
        """Handle suspicious activity event."""
        actions = []
        
        # Log the suspicious activity
        logger.warning(f"Suspicious activity detected for user {user.get('email')}: {event.reason}")
        actions.append("logged_suspicious_activity")
        
        # Could implement additional actions like:
        # - Require re-authentication
        # - Send security alert to user
        # - Increase monitoring
        
        return actions

    async def _handle_account_enabled(self, user: Dict, user_service) -> List[str]:
        """Handle account enabled event."""
        actions = []
        
        # Re-enable user account if it was disabled
        await user_service.enable_user(user["_id"], reason="google_account_enabled")
        actions.append("enabled_user_account")
        
        return actions
