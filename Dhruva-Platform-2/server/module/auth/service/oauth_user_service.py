"""
OAuth user service for managing OAuth users and account linking.
Handles user creation, authentication, and OAuth provider management.
"""

import os
import jwt
from datetime import datetime, timed<PERSON>ta
from typing import Tuple, Optional
from fastapi import HTTPException, status, Depends
from bson.objectid import ObjectId

from module.auth.model.user import User, OAuthProvider
from module.auth.model.session import Session
from module.auth.repository.user_repository import UserRepository
from module.auth.repository.session_repository import SessionRepository
from module.auth.service.auth_service import AuthService
from module.auth.service.email_verification_service import EmailVerificationService
from module.auth.service.google_oauth_service import GoogleUserInfo, GoogleOAuthTokens
from schema.auth.common import RoleType, ApiKeyType
from schema.auth.request import CreateApiKeyRequest
from utils.encryption import encrypt_token, decrypt_token


class OAuthUserService:
    """Service for managing OAuth users and account linking."""

    def __init__(
        self,
        user_repository: UserRepository = Depends(),
        session_repository: SessionRepository = Depends(),
        auth_service: AuthService = Depends(),
        email_verification_service: EmailVerificationService = Depends()
    ):
        self.user_repository = user_repository
        self.session_repository = session_repository
        self.auth_service = auth_service
        self.email_verification_service = email_verification_service
    
    async def handle_oauth_user(
        self,
        provider: str,
        user_info: GoogleUserInfo,
        tokens: GoogleOAuthTokens
    ) -> Tuple[User, str]:
        """
        Handle OAuth user login/registration.
        
        Args:
            provider: OAuth provider name ("google")
            user_info: User information from OAuth provider
            tokens: OAuth tokens from provider
            
        Returns:
            Tuple[User, str]: (user_object, jwt_token)
        """
        # Check if user exists by email
        existing_user = self.user_repository.find_one({"email": user_info.email})
        
        if existing_user:
            # Update existing user with OAuth info
            user = await self._update_existing_user_oauth(
                existing_user, provider, user_info, tokens
            )
        else:
            # Create new OAuth user
            user = await self._create_new_oauth_user(
                provider, user_info, tokens
            )
        
        # Generate JWT token for session
        jwt_token = self._generate_oauth_jwt_token(user, provider)
        
        # Update last login
        user.last_login = datetime.utcnow()
        self.user_repository.save(user)
        
        return user, jwt_token
    
    async def _create_new_oauth_user(
        self,
        provider: str,
        user_info: GoogleUserInfo,
        tokens: GoogleOAuthTokens
    ) -> User:
        """Create new user from OAuth information."""
        
        # Check if email verification is needed
        needs_verification = not user_info.verified_email
        
        if needs_verification:
            # Create pending registration for unverified OAuth email
            await self._create_oauth_pending_registration(user_info, tokens, provider)
            raise HTTPException(
                status_code=status.HTTP_202_ACCEPTED,
                detail={
                    "message": "Email verification required",
                    "email": user_info.email,
                    "verification_required": True,
                    "provider": provider
                }
            )
        
        # Create OAuth provider info
        oauth_provider = OAuthProvider(
            provider=provider,
            provider_user_id=user_info.id,
            email=user_info.email,
            verified_email=user_info.verified_email,
            access_token=encrypt_token(tokens.access_token),
            refresh_token=encrypt_token(tokens.refresh_token) if tokens.refresh_token else None,
            token_expires_at=datetime.utcnow() + timedelta(seconds=tokens.expires_in),
            scope=tokens.scope,
            created_at=datetime.utcnow(),
            last_login=datetime.utcnow()
        )
        
        # Create new user
        new_user = User(
            name=user_info.name,
            email=user_info.email,
            password=None,  # OAuth-only user
            role=RoleType.CONSUMER,
            oauth_providers=[oauth_provider],
            created_via=provider,
            email_verified=user_info.verified_email,
            created_at=datetime.utcnow()
        )
        
        # Save user
        user_id = self.user_repository.insert_one(new_user)
        created_user = self.user_repository.get_by_id(ObjectId(str(user_id)))
        
        # Generate default API key
        try:
            api_request = CreateApiKeyRequest(
                name="default",
                type=ApiKeyType.INFERENCE,
                regenerate=False,
                target_user_id=str(created_user.id),
                data_tracking=False,
            )
            
            self.auth_service.create_api_key(
                request=api_request,
                id=ObjectId(str(created_user.id)),
            )
        except Exception as e:
            print(f"Warning: Failed to create default API key for OAuth user: {e}")
        
        return created_user
    
    async def _update_existing_user_oauth(
        self,
        existing_user: User,
        provider: str,
        user_info: GoogleUserInfo,
        tokens: GoogleOAuthTokens
    ) -> User:
        """Update existing user with OAuth information."""
        
        # Check if user already has this OAuth provider
        existing_oauth = existing_user.get_oauth_provider(provider)
        
        if existing_oauth:
            # Update existing OAuth provider info
            existing_oauth.access_token = encrypt_token(tokens.access_token)
            existing_oauth.refresh_token = encrypt_token(tokens.refresh_token) if tokens.refresh_token else None
            existing_oauth.token_expires_at = datetime.utcnow() + timedelta(seconds=tokens.expires_in)
            existing_oauth.scope = tokens.scope
            existing_oauth.last_login = datetime.utcnow()
            existing_oauth.verified_email = user_info.verified_email
        else:
            # Add new OAuth provider
            oauth_provider = OAuthProvider(
                provider=provider,
                provider_user_id=user_info.id,
                email=user_info.email,
                verified_email=user_info.verified_email,
                access_token=encrypt_token(tokens.access_token),
                refresh_token=encrypt_token(tokens.refresh_token) if tokens.refresh_token else None,
                token_expires_at=datetime.utcnow() + timedelta(seconds=tokens.expires_in),
                scope=tokens.scope,
                created_at=datetime.utcnow(),
                last_login=datetime.utcnow()
            )
            existing_user.oauth_providers.append(oauth_provider)
        
        # Update email verification status if OAuth email is verified
        if user_info.verified_email and not existing_user.email_verified:
            existing_user.email_verified = True
        
        # Save updated user
        self.user_repository.save(existing_user)
        
        return existing_user
    
    def _generate_oauth_jwt_token(self, user: User, provider: str) -> str:
        """Generate JWT token for OAuth user session."""

        # Create a session for OAuth user (similar to traditional auth)
        session = Session(
            user_id=user.id,
            type="refresh",
            timestamp=datetime.utcnow(),
        )

        session_id = self.session_repository.insert_one(session)

        # Generate JWT token compatible with existing auth system
        payload = {
            "sub": str(user.id),
            "name": user.name,
            "exp": (datetime.utcnow() + timedelta(days=365)).timestamp(),  # 1 year expiry
            "iat": datetime.utcnow().timestamp(),
            "sess_id": str(session_id),
        }

        # Get JWT secret key
        jwt_secret = os.environ.get("JWT_SECRET_KEY")
        if not jwt_secret:
            raise ValueError("JWT_SECRET_KEY not configured")

        token = jwt.encode(
            payload,
            jwt_secret,
            algorithm="HS256",
            headers={"tok": "refresh"}  # Use "refresh" to be compatible with existing system
        )

        return token
    
    async def link_oauth_account(
        self,
        user_id: ObjectId,
        provider: str,
        user_info: GoogleUserInfo,
        tokens: GoogleOAuthTokens
    ) -> User:
        """Link OAuth account to existing user."""
        
        user = self.user_repository.get_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Check if user already has this provider
        if user.has_oauth_provider(provider):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{provider.title()} account already linked"
            )
        
        # Check if OAuth email matches user email
        if user.email != user_info.email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="OAuth email does not match account email"
            )
        
        # Add OAuth provider
        oauth_provider = OAuthProvider(
            provider=provider,
            provider_user_id=user_info.id,
            email=user_info.email,
            verified_email=user_info.verified_email,
            access_token=encrypt_token(tokens.access_token),
            refresh_token=encrypt_token(tokens.refresh_token) if tokens.refresh_token else None,
            token_expires_at=datetime.utcnow() + timedelta(seconds=tokens.expires_in),
            scope=tokens.scope,
            created_at=datetime.utcnow(),
            last_login=datetime.utcnow()
        )
        
        user.oauth_providers.append(oauth_provider)
        
        # Update email verification if OAuth email is verified
        if user_info.verified_email and not user.email_verified:
            user.email_verified = True
        
        self.user_repository.save(user)
        return user
    
    async def unlink_oauth_account(self, user_id: ObjectId, provider: str) -> User:
        """Unlink OAuth account from user."""
        
        user = self.user_repository.get_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Check if user has this provider
        if not user.has_oauth_provider(provider):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{provider.title()} account not linked"
            )
        
        # Check if user can safely unlink this provider
        if not user.can_unlink_oauth_provider(provider):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot unlink last authentication method. Please set a password first."
            )
        
        # Remove OAuth provider
        user.oauth_providers = [
            oauth for oauth in user.oauth_providers 
            if oauth.provider != provider
        ]
        
        self.user_repository.save(user)
        return user
    
    async def _create_oauth_pending_registration(
        self,
        user_info: GoogleUserInfo,
        tokens: GoogleOAuthTokens,
        provider: str
    ):
        """Create pending registration for OAuth user with unverified email."""
        
        # This would integrate with the existing email verification service
        # For now, we'll raise an exception to indicate email verification is needed
        # In a full implementation, this would create a pending registration
        # and send a verification email
        
        print(f"OAuth user {user_info.email} needs email verification")
        print(f"Provider: {provider}, Verified: {user_info.verified_email}")
        
        # TODO: Implement OAuth pending registration creation
        # This should integrate with the existing EmailVerificationService

    # RISC Security Event Handling Methods

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email address."""
        try:
            return self.user_repository.find_one({"email": email})
        except Exception:
            return None

    async def get_user_by_oauth_subject(self, subject: str) -> Optional[User]:
        """Get user by OAuth subject (provider user ID)."""
        try:
            return self.user_repository.find_one({
                "oauth_providers.provider_user_id": subject
            })
        except Exception:
            return None

    async def revoke_all_user_tokens(self, user_id: ObjectId) -> bool:
        """Revoke all OAuth tokens for a user (RISC security response)."""
        try:
            user = self.user_repository.get_by_id(user_id)
            if not user:
                return False

            # Clear all OAuth tokens
            for oauth_provider in user.oauth_providers:
                oauth_provider.access_token = None
                oauth_provider.refresh_token = None
                oauth_provider.token_expires_at = datetime.utcnow()  # Mark as expired

            # Revoke all sessions
            self.session_repository.delete_many({"user_id": user_id})

            self.user_repository.save(user)
            return True

        except Exception as e:
            print(f"Error revoking user tokens: {e}")
            return False

    async def revoke_all_user_api_keys(self, user_id: ObjectId) -> bool:
        """Revoke all API keys for a user (RISC security response)."""
        try:
            # This would require access to the API key collection
            # For now, we'll mark this as a placeholder
            # In a full implementation, this would disable all user API keys
            print(f"Revoking all API keys for user {user_id}")

            # TODO: Implement API key revocation
            # This should integrate with the existing API key management system
            return True

        except Exception as e:
            print(f"Error revoking user API keys: {e}")
            return False

    async def disable_user_account(self, user_id: ObjectId, reason: str = "security_event") -> bool:
        """Disable user account (RISC security response)."""
        try:
            user = self.user_repository.get_by_id(user_id)
            if not user:
                return False

            # Add disabled flag and reason
            user.disabled = True
            user.disabled_reason = reason
            user.disabled_at = datetime.utcnow()

            # Revoke all sessions
            self.session_repository.delete_many({"user_id": user_id})

            self.user_repository.save(user)
            return True

        except Exception as e:
            print(f"Error disabling user account: {e}")
            return False

    async def enable_user_account(self, user_id: ObjectId, reason: str = "security_event") -> bool:
        """Enable user account (RISC security response)."""
        try:
            user = self.user_repository.get_by_id(user_id)
            if not user:
                return False

            # Remove disabled flag
            user.disabled = False
            user.disabled_reason = None
            user.disabled_at = None
            user.enabled_at = datetime.utcnow()
            user.enabled_reason = reason

            self.user_repository.save(user)
            return True

        except Exception as e:
            print(f"Error enabling user account: {e}")
            return False

    async def revoke_user_sessions(self, user_id: ObjectId) -> bool:
        """Revoke all user sessions (RISC security response)."""
        try:
            # Revoke all sessions for the user
            result = self.session_repository.delete_many({"user_id": user_id})
            return result.deleted_count > 0

        except Exception as e:
            print(f"Error revoking user sessions: {e}")
            return False
