"""
OAuth router for handling Google OAuth2 authentication flows.
Provides endpoints for OAuth login, callback, account linking, and unlinking.
"""

import os
import secrets
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status, Header
from fastapi.responses import RedirectResponse

from auth.request_session_provider import InjectRequestSession
from exception.client_error import ClientErrorResponse
from module.auth.service.google_oauth_service import GoogleOAuthService
from module.auth.service.oauth_user_service import OAuthUserService
from module.auth.service.risc_service import RISCService
from module.auth.model.oauth_state import OAuthState
from module.auth.repository.oauth_state_repository import OAuthStateRepository
from schema.auth.response.oauth_response import (
    OAuthLinkResponse,
    OAuthUnlinkResponse
)


router = APIRouter(
    prefix="/oauth",
    tags=["OAuth Authentication"],
    responses={"401": {"model": ClientErrorResponse}},
)


@router.get("/google/login")
async def google_oauth_login(
    request: Request,
    redirect_uri: Optional[str] = Query(None, description="Custom redirect URI after successful auth"),
    google_service: GoogleOAuthService = Depends(),
    oauth_state_repo: OAuthStateRepository = Depends()
):
    """
    Initiate Google OAuth2 login flow.
    
    This endpoint starts the OAuth2 authorization code flow with PKCE.
    It generates the necessary security parameters and redirects the user to Google.
    
    Query Parameters:
    - redirect_uri: Optional custom redirect URI after successful authentication
    
    Returns:
    - 302 Redirect to Google OAuth authorization URL
    """
    try:
        # Generate PKCE parameters for security
        code_verifier, code_challenge = google_service.generate_pkce_pair()
        
        # Generate state parameter for CSRF protection
        state = secrets.token_urlsafe(32)
        
        # Get redirect URI from request or use default
        final_redirect_uri = redirect_uri or request.headers.get("referer", "/")
        
        # Store OAuth state temporarily for validation
        oauth_state = OAuthState.create_for_login(
            state=state,
            code_verifier=code_verifier,
            code_challenge=code_challenge,
            provider="google",
            redirect_uri=final_redirect_uri,
            expiry_minutes=10
        )
        
        oauth_state_repo.insert_one(oauth_state)
        
        # Generate authorization URL
        auth_url = google_service.generate_authorization_url(
            state=state,
            code_challenge=code_challenge
        )
        
        # Redirect to Google OAuth
        return RedirectResponse(url=auth_url, status_code=302)
        
    except Exception as e:
        # Redirect to error page instead of raising exception
        error_url = f"{os.environ.get('OAUTH_ERROR_REDIRECT_URL', '/')}?error=init_failed"
        return RedirectResponse(url=error_url, status_code=302)


@router.get("/google/callback")
async def google_oauth_callback(
    code: str = Query(..., description="Authorization code from Google"),
    state: str = Query(..., description="State parameter for CSRF protection"),
    error: Optional[str] = Query(None, description="Error from Google OAuth"),
    google_service: GoogleOAuthService = Depends(),
    oauth_user_service: OAuthUserService = Depends(),
    oauth_state_repo: OAuthStateRepository = Depends()
):
    """
    Handle Google OAuth2 callback.
    
    This endpoint processes the OAuth2 callback from Google, validates the state,
    exchanges the authorization code for tokens, and creates or updates the user.
    
    Query Parameters:
    - code: Authorization code from Google
    - state: State parameter for CSRF protection
    - error: Error from Google (if any)
    
    Returns:
    - 302 Redirect to success or error page
    """
    try:
        # Handle OAuth errors from Google
        if error:
            error_url = f"{os.environ.get('OAUTH_ERROR_REDIRECT_URL', '/')}?error={error}"
            return RedirectResponse(url=error_url, status_code=302)
        
        # Validate and retrieve OAuth state
        oauth_state = oauth_state_repo.find_valid_state(state, "google")
        if not oauth_state:
            error_url = f"{os.environ.get('OAUTH_ERROR_REDIRECT_URL', '/')}?error=invalid_state"
            return RedirectResponse(url=error_url, status_code=302)
        
        # Exchange authorization code for tokens
        tokens = await google_service.exchange_code_for_tokens(
            code=code,
            code_verifier=oauth_state.code_verifier
        )
        
        # Get user information from Google
        user_info = await google_service.get_user_info(tokens.access_token)
        
        # Create or update user
        user, jwt_token = await oauth_user_service.handle_oauth_user(
            provider="google",
            user_info=user_info,
            tokens=tokens
        )
        
        # Clean up OAuth state
        oauth_state_repo.delete_by_state(state)
        
        # Redirect to success page with token
        success_url = f"{os.environ.get('OAUTH_SUCCESS_REDIRECT_URL', '/')}?token={jwt_token}"
        return RedirectResponse(url=success_url, status_code=302)
        
    except HTTPException as e:
        # Handle specific HTTP exceptions
        if e.status_code == status.HTTP_202_ACCEPTED:
            # Email verification required
            verification_url = f"{os.environ.get('OAUTH_ERROR_REDIRECT_URL', '/')}?error=verification_required"
            return RedirectResponse(url=verification_url, status_code=302)
        else:
            error_url = f"{os.environ.get('OAUTH_ERROR_REDIRECT_URL', '/')}?error=auth_failed"
            return RedirectResponse(url=error_url, status_code=302)
    except Exception as e:
        # Handle unexpected errors
        print(f"OAuth callback error: {e}")
        error_url = f"{os.environ.get('OAUTH_ERROR_REDIRECT_URL', '/')}?error=server_error"
        return RedirectResponse(url=error_url, status_code=302)


@router.post("/google/link", response_model=OAuthLinkResponse)
async def link_google_account(
    code: str,
    state: str,
    request_session=Depends(InjectRequestSession),
    google_service: GoogleOAuthService = Depends(),
    oauth_user_service: OAuthUserService = Depends(),
    oauth_state_repo: OAuthStateRepository = Depends()
):
    """
    Link Google OAuth account to existing authenticated user.
    
    This endpoint allows an authenticated user to link their Google account
    to their existing Dhruva Platform account.
    
    Requires existing authentication (JWT token).
    
    Body Parameters:
    - code: Authorization code from Google
    - state: State parameter for CSRF protection
    
    Returns:
    - Success message with linked account information
    """
    try:
        # Validate OAuth state
        oauth_state = oauth_state_repo.find_valid_state(state, "google")
        if not oauth_state:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired state parameter"
            )
        
        # Exchange authorization code for tokens
        tokens = await google_service.exchange_code_for_tokens(
            code=code,
            code_verifier=oauth_state.code_verifier
        )
        
        # Get user information from Google
        user_info = await google_service.get_user_info(tokens.access_token)
        
        # Link OAuth account to existing user
        updated_user = await oauth_user_service.link_oauth_account(
            user_id=request_session.id,
            provider="google",
            user_info=user_info,
            tokens=tokens
        )
        
        # Clean up OAuth state
        oauth_state_repo.delete_by_state(state)
        
        return OAuthLinkResponse(
            message="Google account linked successfully",
            provider="google",
            email=user_info.email,
            linked_at=datetime.utcnow()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to link Google account: {str(e)}"
        )


@router.delete("/google/unlink", response_model=OAuthUnlinkResponse)
async def unlink_google_account(
    request_session=Depends(InjectRequestSession),
    oauth_user_service: OAuthUserService = Depends()
):
    """
    Unlink Google OAuth account from authenticated user.
    
    This endpoint allows an authenticated user to unlink their Google account
    from their Dhruva Platform account.
    
    Requires existing authentication (JWT token).
    
    Returns:
    - Success message confirming account unlinking
    """
    try:
        updated_user = await oauth_user_service.unlink_oauth_account(
            user_id=request_session.id,
            provider="google"
        )
        
        return OAuthUnlinkResponse(
            message="Google account unlinked successfully",
            provider="google",
            unlinked_at=datetime.utcnow()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to unlink Google account: {str(e)}"
        )


@router.get("/google/status")
async def get_google_oauth_status(
    request_session=Depends(InjectRequestSession)
):
    """
    Get Google OAuth status for authenticated user.
    
    Returns information about whether the user has linked their Google account.
    
    Requires existing authentication (JWT token).
    
    Returns:
    - OAuth provider status and information
    """
    try:
        # This would require getting the user from the database
        # For now, return basic status based on session
        return {
            "provider": "google",
            "linked": False,  # Would check user's oauth_providers
            "user_id": str(request_session.id),
            "message": "OAuth status retrieved successfully"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get OAuth status: {str(e)}"
        )


@router.post("/google/risc")
async def handle_risc_webhook(
    request: Request,
    x_goog_signature: Optional[str] = Header(None, alias="X-Goog-Signature"),
    risc_service: RISCService = Depends(),
    oauth_user_service: OAuthUserService = Depends()
):
    """
    Handle RISC (Risk and Incident Sharing and Communications) webhook from Google.

    This endpoint receives security events from Google about user accounts
    and takes appropriate security actions.

    Headers:
    - X-Goog-Signature: Webhook signature for verification

    Returns:
    - RISC event processing response
    """
    try:
        # Get raw request body for signature verification
        body = await request.body()

        # Verify webhook signature
        if x_goog_signature and not risc_service.verify_webhook_signature(body, x_goog_signature):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid webhook signature"
            )

        # Parse JSON payload
        try:
            payload = json.loads(body.decode('utf-8'))
        except json.JSONDecodeError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid JSON payload"
            )

        # Parse RISC event
        risc_event = risc_service.parse_risc_event(payload)

        # Process security event
        # Note: We'll need to inject user_service dependency here
        # For now, we'll pass oauth_user_service which has user operations
        response = await risc_service.process_security_event(
            event=risc_event,
            user_service=oauth_user_service,  # Using oauth_user_service for user operations
            oauth_user_service=oauth_user_service
        )

        return {
            "event_id": response.event_id,
            "processed": response.processed,
            "actions_taken": response.actions_taken,
            "timestamp": response.timestamp.isoformat(),
            "error": response.error
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process RISC webhook: {str(e)}"
        )
