from datetime import datetime
from typing import List, Optional
from db.MongoBaseModel import MongoBaseModel
from pydantic import BaseModel, EmailStr
from schema.auth.common import RoleType


class OAuthProvider(BaseModel):
    """OAuth provider information for a user."""
    provider: str  # "google", "github", "microsoft"
    provider_user_id: str
    email: EmailStr
    verified_email: bool
    access_token: str  # Encrypted
    refresh_token: Optional[str] = None  # Encrypted
    token_expires_at: Optional[datetime] = None
    scope: str  # OAuth scopes granted
    created_at: datetime
    last_login: datetime


class User(MongoBaseModel):
    name: str
    email: EmailStr
    password: Optional[str] = None  # Optional for OAuth-only users
    role: RoleType
    oauth_providers: List[OAuthProvider] = []
    created_via: str = "email"  # "email", "google", "github", "microsoft"
    email_verified: bool = False
    created_at: Optional[datetime] = None
    last_login: Optional[datetime] = None

    # RISC Security Fields
    disabled: bool = False
    disabled_reason: Optional[str] = None
    disabled_at: Optional[datetime] = None
    enabled_at: Optional[datetime] = None
    enabled_reason: Optional[str] = None

    def get_oauth_provider(self, provider: str) -> Optional[OAuthProvider]:
        """Get OAuth provider info by provider name."""
        for oauth_provider in self.oauth_providers:
            if oauth_provider.provider == provider:
                return oauth_provider
        return None

    def has_oauth_provider(self, provider: str) -> bool:
        """Check if user has a specific OAuth provider linked."""
        return self.get_oauth_provider(provider) is not None

    def can_unlink_oauth_provider(self, provider: str) -> bool:
        """Check if user can safely unlink an OAuth provider."""
        # User must have either a password or another OAuth provider
        if self.password:
            return True

        # Count other OAuth providers
        other_providers = [
            oauth for oauth in self.oauth_providers
            if oauth.provider != provider
        ]
        return len(other_providers) > 0
