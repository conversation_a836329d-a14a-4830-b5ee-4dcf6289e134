#!/usr/bin/env python3
"""
Test script to verify OAuth imports and dependencies.
"""

import sys
import os

# Add server directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'server'))

def test_oauth_imports():
    """Test all OAuth-related imports."""
    
    print("🔍 Testing OAuth imports...")
    
    # Test basic dependencies
    try:
        import httpx
        print("✅ httpx imported successfully")
    except ImportError as e:
        print(f"❌ httpx import failed: {e}")
        return False
    
    try:
        import cryptography
        print("✅ cryptography imported successfully")
    except ImportError as e:
        print(f"❌ cryptography import failed: {e}")
        return False
    
    # Test OAuth service imports
    try:
        from module.auth.service.google_oauth_service import GoogleOAuthService, GoogleUserInfo, GoogleOAuthTokens
        print("✅ Google OAuth service imported successfully")
    except ImportError as e:
        print(f"❌ Google OAuth service import failed: {e}")
        return False
    
    # Test OAuth state model
    try:
        from module.auth.model.oauth_state import OAuthState
        print("✅ OAuth state model imported successfully")
    except ImportError as e:
        print(f"❌ OAuth state model import failed: {e}")
        return False
    
    # Test OAuth state repository
    try:
        from module.auth.repository.oauth_state_repository import OAuthStateRepository
        print("✅ OAuth state repository imported successfully")
    except ImportError as e:
        print(f"❌ OAuth state repository import failed: {e}")
        return False
    
    # Test OAuth user service
    try:
        from module.auth.service.oauth_user_service import OAuthUserService
        print("✅ OAuth user service imported successfully")
    except ImportError as e:
        print(f"❌ OAuth user service import failed: {e}")
        return False
    
    # Test encryption utilities
    try:
        from utils.encryption import encrypt_token, decrypt_token
        print("✅ Encryption utilities imported successfully")
    except ImportError as e:
        print(f"❌ Encryption utilities import failed: {e}")
        return False
    
    # Test OAuth router
    try:
        from module.auth.router.oauth_router import router as oauth_router
        print("✅ OAuth router imported successfully")
        print(f"   Router prefix: {oauth_router.prefix}")
        print(f"   Number of routes: {len(oauth_router.routes)}")
        for route in oauth_router.routes:
            print(f"   Route: {route.methods} {route.path}")
    except ImportError as e:
        print(f"❌ OAuth router import failed: {e}")
        return False
    
    # Test OAuth configuration
    try:
        service = GoogleOAuthService()
        print("✅ Google OAuth service instantiated successfully")
        print(f"   Client ID configured: {not service.config.CLIENT_ID.startswith('your-')}")
        print(f"   Client Secret configured: {not service.config.CLIENT_SECRET.startswith('your-')}")
        print(f"   Redirect URI: {service.config.REDIRECT_URI}")
    except Exception as e:
        print(f"❌ Google OAuth service instantiation failed: {e}")
        return False
    
    print("\n🎉 All OAuth imports successful!")
    return True

def test_oauth_endpoints():
    """Test OAuth endpoint availability."""
    
    print("\n🔍 Testing OAuth endpoint availability...")
    
    try:
        import requests
        
        # Test OAuth login endpoint
        response = requests.get("https://13.203.149.17/auth/oauth/google/login", 
                              verify=False, timeout=10)
        
        if response.status_code == 404:
            print("❌ OAuth login endpoint not found (404)")
            return False
        elif response.status_code == 302:
            print("✅ OAuth login endpoint working (302 redirect)")
            return True
        else:
            print(f"⚠️  OAuth login endpoint returned status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to test OAuth endpoints: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting OAuth integration test...\n")
    
    imports_ok = test_oauth_imports()
    
    if imports_ok:
        endpoints_ok = test_oauth_endpoints()
        
        if endpoints_ok:
            print("\n✅ OAuth integration is working correctly!")
        else:
            print("\n❌ OAuth endpoints are not working correctly!")
    else:
        print("\n❌ OAuth imports failed - cannot test endpoints!")
