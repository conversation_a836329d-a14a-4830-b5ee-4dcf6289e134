# Google Cloud Console OAuth Configuration Fix

## 🚨 **Current Issue**
**Error**: "Access blocked: Authorization Error - Error 400: invalid_request"

This error indicates that the Google Cloud Console OAuth configuration needs to be updated.

## 🔧 **Required Google Cloud Console Fixes**

### **1. OAuth Consent Screen Configuration**

#### **Step 1: Access OAuth Consent Screen**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project: `dhruva-platform` (or the project containing your OAuth credentials)
3. Navigate to **APIs & Services** → **OAuth consent screen**

#### **Step 2: Configure OAuth Consent Screen**
```
User Type: External (if you want any Google user to login)
         OR Internal (if you have a Google Workspace domain)

Application Information:
- App name: Dhruva Platform
- User support email: <EMAIL>
- Developer contact information: <EMAIL>

Application Domain:
- Application home page: https://*************/dhruva
- Application privacy policy link: https://*************/dhruva/privacy (create if needed)
- Application terms of service link: https://*************/dhruva/terms (create if needed)

Authorized domains:
- *************

Scopes:
- openid
- email
- profile
```

#### **Step 3: Publishing Status**
- If **User Type = External**: You need to publish the app or add test users
- If **User Type = Internal**: The app is automatically available to your organization

### **2. OAuth Credentials Configuration**

#### **Step 1: Access Credentials**
1. Navigate to **APIs & Services** → **Credentials**
2. Find your OAuth 2.0 Client ID: `************-lhf7ekmbta2hjp0dd580lvm0301o1lue.apps.googleusercontent.com`

#### **Step 2: Update Authorized Redirect URIs**
```
Authorized JavaScript origins:
- https://*************

Authorized redirect URIs:
- https://*************/auth/oauth/google/callback
```

#### **Step 3: Verify Client Configuration**
```
Application type: Web application
Name: Dhruva Platform OAuth Client
```

### **3. Test Users Configuration (If External App)**

If your app is **External** and not published, you need to add test users:

#### **Step 1: Add Test Users**
1. Go to **OAuth consent screen**
2. Scroll down to **Test users**
3. Click **Add users**
4. Add the Gmail addresses that should be able to test the OAuth:
   ```
   <EMAIL>
   <EMAIL>
   ```

### **4. App Publishing (For Production)**

For production use with any Google user:

#### **Step 1: Publish the App**
1. Go to **OAuth consent screen**
2. Click **Publish App**
3. Submit for verification if required

#### **Step 2: Verification Requirements**
- Privacy Policy URL (required)
- Terms of Service URL (required)
- App verification (for sensitive scopes)

## 🛠 **Quick Fix Steps**

### **Immediate Fix (For Testing)**
1. **Set User Type to External**
2. **Add your Gmail as a test user**
3. **Verify redirect URI**: `https://*************/auth/oauth/google/callback`
4. **Save all changes**

### **Configuration Checklist**
- [ ] OAuth consent screen configured
- [ ] App name and contact info filled
- [ ] Authorized domains added (*************)
- [ ] Redirect URI configured correctly
- [ ] Test users added (if external app)
- [ ] Scopes configured (openid, email, profile)

## 🧪 **Testing After Fix**

### **Test OAuth Flow**
1. Clear browser cache/cookies
2. Visit: `https://*************/dhruva/`
3. Click "Login with Google"
4. Should redirect to Google OAuth consent screen
5. Should show your app name and permissions
6. After consent, should redirect back to platform

### **Expected OAuth Consent Screen**
```
Sign in with Google

Dhruva Platform wants to access your Google Account

This will allow Dhruva Platform to:
- See your personal info, including any personal info you've made publicly available
- See your primary Google Account email address

Continue    Cancel
```

## 🔍 **Debugging OAuth Issues**

### **Common Error Codes**
- **400 invalid_request**: Redirect URI not configured
- **403 access_denied**: User denied consent or not in test users
- **400 redirect_uri_mismatch**: Redirect URI doesn't match console config

### **Debug Steps**
1. **Check redirect URI**: Must exactly match console configuration
2. **Verify test users**: Email must be added to test users list
3. **Check app status**: Must be published or user must be test user
4. **Clear browser data**: OAuth state might be cached

### **OAuth URL Analysis**
Current OAuth URL parameters:
```
client_id=************-lhf7ekmbta2hjp0dd580lvm0301o1lue.apps.googleusercontent.com
redirect_uri=https://*************/auth/oauth/google/callback
scope=openid email profile
response_type=code
state=<random-state>
code_challenge=<pkce-challenge>
code_challenge_method=S256
```

## 📋 **Action Items**

### **Immediate Actions**
1. **Update Google Cloud Console OAuth consent screen**
2. **Add test users (including <EMAIL>)**
3. **Verify redirect URI configuration**
4. **Test OAuth flow**

### **Production Readiness**
1. **Create privacy policy page**
2. **Create terms of service page**
3. **Publish OAuth app**
4. **Submit for verification if needed**

## 🎯 **Expected Outcome**

After fixing the Google Cloud Console configuration:
1. OAuth consent screen will show your app name
2. Users can grant permissions to the app
3. OAuth flow will complete successfully
4. Users will be redirected back to the platform
5. JWT tokens will be issued and stored
6. Users can access all platform features

## 🔗 **Useful Links**

- [Google Cloud Console](https://console.cloud.google.com/)
- [OAuth 2.0 Scopes](https://developers.google.com/identity/protocols/oauth2/scopes)
- [OAuth Consent Screen Guide](https://support.google.com/cloud/answer/10311615)
- [OAuth Verification Requirements](https://support.google.com/cloud/answer/9110914)

## ⚠️ **Important Notes**

1. **Redirect URI must match exactly** (including https://)
2. **Test users are required** for external apps in testing
3. **App publishing is required** for production use
4. **Privacy policy is mandatory** for published apps
5. **Domain verification** may be required for some configurations
