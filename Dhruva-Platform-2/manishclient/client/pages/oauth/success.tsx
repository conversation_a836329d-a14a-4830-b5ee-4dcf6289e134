import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { 
  Box, 
  Center, 
  Spinner, 
  Text, 
  VStack, 
  Icon,
  Button,
  useToast
} from "@chakra-ui/react";
import { CheckCircleIcon } from "@chakra-ui/icons";
import Head from "next/head";

export default function OAuthSuccess() {
  const router = useRouter();
  const toast = useToast();
  const [isProcessing, setIsProcessing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (router.isReady) {
      const { token, error } = router.query;

      if (error) {
        setError(error as string);
        setIsProcessing(false);
        
        toast({
          title: "Authentication Failed",
          description: getErrorMessage(error as string),
          status: "error",
          duration: 5000,
          isClosable: true,
        });
        return;
      }

      if (token) {
        try {
          // Store the JWT token as both access and refresh token
          // This matches the existing authentication pattern
          localStorage.setItem("access_token", token as string);
          localStorage.setItem("refresh_token", token as string);
          
          // Store user info if available in token (decode JWT)
          try {
            const payload = JSON.parse(atob((token as string).split('.')[1]));
            if (payload.email) {
              localStorage.setItem("email", payload.email);
            }
            if (payload.role) {
              localStorage.setItem("user_role", payload.role);
            }
            if (payload.sub) {
              localStorage.setItem("user_id", payload.sub);
            }
          } catch (e) {
            console.warn("Could not decode JWT token:", e);
          }

          setIsProcessing(false);

          // Show success message
          toast({
            title: "Login Successful",
            description: "You have been successfully authenticated with Google.",
            status: "success",
            duration: 3000,
            isClosable: true,
          });

          // Redirect after a short delay
          setTimeout(() => {
            const currentPage = localStorage.getItem("current_page");
            if (currentPage && currentPage !== window.location.href) {
              window.location.href = currentPage;
            } else {
              router.push("/testing-ground");
            }
          }, 2000);

        } catch (error) {
          console.error("Error processing OAuth success:", error);
          setError("token_processing_error");
          setIsProcessing(false);
        }
      } else {
        setError("missing_token");
        setIsProcessing(false);
      }
    }
  }, [router, toast]);

  const getErrorMessage = (errorCode: string): string => {
    switch (errorCode) {
      case "verification_required":
        return "Email verification is required. Please check your email and verify your account.";
      case "auth_failed":
        return "Authentication failed. Please try again.";
      case "server_error":
        return "Server error occurred. Please try again later.";
      case "token_processing_error":
        return "Error processing authentication token. Please try logging in again.";
      case "missing_token":
        return "Authentication token is missing. Please try logging in again.";
      default:
        return "An unexpected error occurred during authentication.";
    }
  };

  const handleRetry = () => {
    router.push("/");
  };

  if (error) {
    return (
      <>
        <Head>
          <title>Authentication Error - Dhruva Platform</title>
        </Head>
        <Center h="100vh" bg="gray.50">
          <VStack spacing={6} textAlign="center" p={8}>
            <Icon as={CheckCircleIcon} w={16} h={16} color="red.500" />
            <Text fontSize="2xl" fontWeight="bold" color="gray.800">
              Authentication Failed
            </Text>
            <Text fontSize="lg" color="gray.600" maxW="md">
              {getErrorMessage(error)}
            </Text>
            <Button colorScheme="blue" onClick={handleRetry}>
              Try Again
            </Button>
          </VStack>
        </Center>
      </>
    );
  }

  if (isProcessing) {
    return (
      <>
        <Head>
          <title>Processing Login - Dhruva Platform</title>
        </Head>
        <Center h="100vh" bg="gray.50">
          <VStack spacing={6} textAlign="center">
            <Spinner size="xl" color="blue.500" thickness="4px" />
            <Text fontSize="xl" fontWeight="semibold" color="gray.700">
              Processing your Google login...
            </Text>
            <Text fontSize="md" color="gray.500">
              Please wait while we complete your authentication.
            </Text>
          </VStack>
        </Center>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Login Successful - Dhruva Platform</title>
      </Head>
      <Center h="100vh" bg="gray.50">
        <VStack spacing={6} textAlign="center" p={8}>
          <Icon as={CheckCircleIcon} w={16} h={16} color="green.500" />
          <Text fontSize="2xl" fontWeight="bold" color="gray.800">
            Login Successful!
          </Text>
          <Text fontSize="lg" color="gray.600">
            You have been successfully authenticated with Google.
          </Text>
          <Text fontSize="sm" color="gray.500">
            Redirecting you to the platform...
          </Text>
        </VStack>
      </Center>
    </>
  );
}
