import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { 
  Box, 
  Center, 
  Text, 
  VStack, 
  Icon,
  Button,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  useToast
} from "@chakra-ui/react";
import { WarningIcon } from "@chakra-ui/icons";
import Head from "next/head";

export default function OAuthError() {
  const router = useRouter();
  const toast = useToast();
  const [errorCode, setErrorCode] = useState<string | null>(null);
  const [errorDescription, setErrorDescription] = useState<string>("");

  useEffect(() => {
    if (router.isReady) {
      const { error, error_description } = router.query;
      
      if (error) {
        setErrorCode(error as string);
        setErrorDescription(error_description as string || "");
        
        // Show toast notification
        toast({
          title: "Authentication Error",
          description: getErrorMessage(error as string),
          status: "error",
          duration: 8000,
          isClosable: true,
        });
      }
    }
  }, [router, toast]);

  const getErrorMessage = (errorCode: string): string => {
    switch (errorCode) {
      case "access_denied":
        return "You denied access to your Google account. Please try again and grant the necessary permissions.";
      case "verification_required":
        return "Email verification is required. Please check your email and verify your account before proceeding.";
      case "auth_failed":
        return "Authentication failed. There was an issue with the Google OAuth process.";
      case "server_error":
        return "A server error occurred during authentication. Please try again later.";
      case "invalid_request":
        return "Invalid OAuth request. Please try logging in again.";
      case "unauthorized_client":
        return "OAuth client is not authorized. Please contact support.";
      case "unsupported_response_type":
        return "Unsupported OAuth response type. Please contact support.";
      case "invalid_scope":
        return "Invalid OAuth scope requested. Please contact support.";
      case "temporarily_unavailable":
        return "Google OAuth service is temporarily unavailable. Please try again later.";
      default:
        return "An unexpected error occurred during Google authentication. Please try again.";
    }
  };

  const getErrorTitle = (errorCode: string): string => {
    switch (errorCode) {
      case "access_denied":
        return "Access Denied";
      case "verification_required":
        return "Email Verification Required";
      case "auth_failed":
        return "Authentication Failed";
      case "server_error":
        return "Server Error";
      case "temporarily_unavailable":
        return "Service Temporarily Unavailable";
      default:
        return "Authentication Error";
    }
  };

  const getAlertStatus = (errorCode: string): "error" | "warning" | "info" => {
    switch (errorCode) {
      case "verification_required":
        return "warning";
      case "temporarily_unavailable":
        return "info";
      default:
        return "error";
    }
  };

  const handleRetryLogin = () => {
    router.push("/");
  };

  const handleContactSupport = () => {
    // You can customize this to your support contact method
    window.open("mailto:<EMAIL>?subject=OAuth Authentication Issue", "_blank");
  };

  const shouldShowContactSupport = (errorCode: string): boolean => {
    const supportErrors = [
      "unauthorized_client",
      "unsupported_response_type", 
      "invalid_scope",
      "server_error"
    ];
    return supportErrors.includes(errorCode);
  };

  const shouldShowRetry = (errorCode: string): boolean => {
    const retryableErrors = [
      "access_denied",
      "auth_failed",
      "invalid_request",
      "temporarily_unavailable"
    ];
    return retryableErrors.includes(errorCode);
  };

  return (
    <>
      <Head>
        <title>Authentication Error - Dhruva Platform</title>
      </Head>
      <Center h="100vh" bg="gray.50" p={4}>
        <Box maxW="md" w="full">
          <VStack spacing={6} textAlign="center">
            <Icon as={WarningIcon} w={16} h={16} color="red.500" />
            
            <Text fontSize="2xl" fontWeight="bold" color="gray.800">
              {errorCode ? getErrorTitle(errorCode) : "Authentication Error"}
            </Text>

            {errorCode && (
              <Alert 
                status={getAlertStatus(errorCode)} 
                borderRadius="md"
                flexDirection="column"
                alignItems="center"
                textAlign="center"
                p={4}
              >
                <AlertIcon boxSize="40px" mr={0} />
                <AlertTitle mt={4} mb={1} fontSize="lg">
                  {getErrorTitle(errorCode)}
                </AlertTitle>
                <AlertDescription maxWidth="sm" fontSize="md">
                  {getErrorMessage(errorCode)}
                </AlertDescription>
              </Alert>
            )}

            {errorDescription && (
              <Box 
                bg="gray.100" 
                p={4} 
                borderRadius="md" 
                w="full"
                fontSize="sm"
                color="gray.600"
              >
                <Text fontWeight="semibold" mb={2}>Technical Details:</Text>
                <Text>{errorDescription}</Text>
              </Box>
            )}

            <VStack spacing={3} w="full">
              {(!errorCode || shouldShowRetry(errorCode)) && (
                <Button 
                  colorScheme="blue" 
                  size="lg" 
                  w="full"
                  onClick={handleRetryLogin}
                >
                  Try Login Again
                </Button>
              )}

              {errorCode === "verification_required" && (
                <Button 
                  colorScheme="orange" 
                  variant="outline"
                  size="lg" 
                  w="full"
                  onClick={() => window.open("https://mail.google.com", "_blank")}
                >
                  Check Email for Verification
                </Button>
              )}

              {errorCode && shouldShowContactSupport(errorCode) && (
                <Button 
                  colorScheme="gray" 
                  variant="outline"
                  size="lg" 
                  w="full"
                  onClick={handleContactSupport}
                >
                  Contact Support
                </Button>
              )}

              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => router.push("/")}
              >
                Back to Login
              </Button>
            </VStack>

            {errorCode && (
              <Text fontSize="xs" color="gray.400" mt={4}>
                Error Code: {errorCode}
              </Text>
            )}
          </VStack>
        </Box>
      </Center>
    </>
  );
}
