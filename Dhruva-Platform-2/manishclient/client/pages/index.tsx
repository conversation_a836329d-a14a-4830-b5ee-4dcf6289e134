import {
  <PERSON>rid,
  <PERSON>rid<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ack,
  Input,
  Button,
  useMediaQuery,
  useToast,
} from "@chakra-ui/react";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import { login } from "../api/authAPI";
import { useMutation } from "@tanstack/react-query";
import BaseImage from "../components/Common/BaseImage";

export default function Login() {
  const router = useRouter();
  const toast = useToast();
  const [isMobile] = useMediaQuery("(max-width: 768px)");
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");

  const mutation = useMutation(login);

  useEffect(() => {
    if (
      localStorage.getItem("refresh_token") &&
      localStorage.getItem("access_token")
    ) {
      const currentPage = localStorage.getItem("current_page");
      if (currentPage) {
        // If current page is stored, redirect there
        window.location.href = currentPage;
      } else {
        // Default redirect to testing ground
        router.push("/testing-ground");
      }
    }
  }, [router]);

  const validateCredentials = () => {
    mutation.mutate(
      { email: username, password: password },
      {
        onSuccess: () => {
          localStorage.setItem("email", username);
          const currentPage = localStorage.getItem("current_page");
          if (currentPage) {
            // Use window.location.href for full page redirect with base path
            window.location.href = currentPage;
          } else {
            router.push("/testing-ground");
          }
        },
        onError: (error: any) => {
          if (
            error?.response.status === 401 ||
            error?.response.status === 422
          ) {
            toast({
              title: "Error",
              description: "Invalid Credentials",
              status: "error",
              duration: 5000,
              isClosable: true,
            });
          } else {
            toast({
              title: "Error",
              description: "Something went wrong, please try again later",
              status: "error",
              duration: 5000,
              isClosable: true,
            });
          }
        },
      }
    );
  };

  const handleGoogleLogin = () => {
    try {
      // Store current page for redirect after OAuth
      localStorage.setItem("current_page", window.location.href);

      // Show loading toast
      toast({
        title: "Redirecting to Google",
        description: "Please wait while we redirect you to Google for authentication...",
        status: "info",
        duration: 3000,
        isClosable: true,
      });

      // Redirect to OAuth endpoint with proper error handling
      const oauthUrl = "http://localhost:8000/auth/oauth/google/login";
      window.location.href = oauthUrl;
    } catch (error) {
      console.error("Error initiating Google OAuth:", error);
      toast({
        title: "OAuth Error",
        description: "Failed to initiate Google authentication. Please try again.",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  return (
    <>
      <Head>
        <title>Login into Dhruva</title>
      </Head>
      {isMobile ? (
        <Grid templateColumns="repeat(1, 1fr)">
          <GridItem className="centered-column" w="100%" h="100vh">
            <Stack spacing={5}>
              <BaseImage src="/a4b.svg" width={104} height={104} alt="a4b" />
              <Heading>Login into Dhruva</Heading>
              <Input
                value={username}
                type="username"
                onChange={(e) => {
                  setUsername(e.target.value);
                }}
                placeholder="Username"
                size="lg"
              />
              <Input
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                }}
                type="password"
                placeholder="Password"
                size="lg"
              />
              <Button
                onClick={() => {
                  validateCredentials();
                }}
              >
                LOGIN
              </Button>
              <Button
                onClick={handleGoogleLogin}
                colorScheme="red"
                variant="outline"
                size="lg"
                leftIcon={<img src="/google-logo.svg" alt="Google" style={{ width: 20, height: 20 }} />}
                _hover={{ bg: "red.50" }}
              >
                Continue with Google
              </Button>
            </Stack>
          </GridItem>
        </Grid>
      ) : (
        <Grid templateColumns="repeat(2, 1fr)">
          <GridItem
            className="centered-column"
            w="100%"
            h="100vh"
            bg="gray.100"
          >
            <BaseImage
              src="/dhruvaai.svg"
              width={500}
              height={500}
              alt="dhruvabot"
            />
          </GridItem>
          <GridItem className="centered-column" w="100%" h="100vh">
            <Stack spacing={5}>
              <BaseImage src="/a4b.svg" width={104} height={104} alt="a4b" />
              <Heading>Login into Dhruva</Heading>
              <Input
                value={username}
                type="username"
                onChange={(e) => {
                  setUsername(e.target.value);
                }}
                placeholder="Username"
                size="lg"
              />
              <Input
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                }}
                type="password"
                placeholder="Password"
                size="lg"
              />
              <Button
                onClick={() => {
                  validateCredentials();
                }}
              >
                LOGIN
              </Button>
              <Button
                onClick={handleGoogleLogin}
                colorScheme="red"
                variant="outline"
                size="lg"
                leftIcon={<img src="/google-logo.svg" alt="Google" style={{ width: 20, height: 20 }} />}
                _hover={{ bg: "red.50" }}
              >
                Continue with Google
              </Button>
            </Stack>
          </GridItem>
        </Grid>
      )}
    </>
  );
}
