import { useState } from "react";
import { 
  <PERSON>, 
  <PERSON>ton, 
  VStack, 
  Text, 
  Heading, 
  Alert, 
  AlertIcon,
  Code,
  useToast
} from "@chakra-ui/react";
import Head from "next/head";

export default function OAuthTest() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const toast = useToast();

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testOAuthEndpoint = async () => {
    addResult("Testing OAuth endpoint...");
    
    try {
      const response = await fetch("https://*************/auth/oauth/google/login", {
        method: "GET",
        redirect: "manual" // Don't follow redirects
      });
      
      addResult(`OAuth endpoint status: ${response.status}`);
      addResult(`OAuth endpoint headers: ${JSON.stringify(Object.fromEntries(response.headers))}`);
      
      if (response.status === 302) {
        const location = response.headers.get("location");
        addResult(`Redirect location: ${location}`);
        
        if (location && location.includes("accounts.google.com")) {
          addResult("✅ OAuth endpoint working correctly - redirects to Google");
        } else {
          addResult("❌ OAuth endpoint not redirecting to Google");
        }
      } else {
        addResult("❌ OAuth endpoint not returning 302 redirect");
      }
    } catch (error) {
      addResult(`❌ Error testing OAuth endpoint: ${error}`);
    }
  };

  const testDirectOAuth = () => {
    addResult("Testing direct OAuth redirect...");
    
    try {
      // Store current page for redirect after OAuth
      localStorage.setItem("current_page", window.location.href);
      addResult("Stored current page in localStorage");
      
      // Show loading toast
      toast({
        title: "Redirecting to Google",
        description: "Testing OAuth redirect...",
        status: "info",
        duration: 3000,
        isClosable: true,
      });
      
      addResult("Redirecting to OAuth endpoint...");
      
      // Redirect to OAuth endpoint
      window.location.href = "https://*************/auth/oauth/google/login";
    } catch (error) {
      addResult(`❌ Error during OAuth redirect: ${error}`);
    }
  };

  const testFrontendPages = async () => {
    addResult("Testing frontend OAuth pages...");
    
    const pages = [
      "/dhruva/oauth/success",
      "/dhruva/oauth/error",
      "/dhruva/"
    ];
    
    for (const page of pages) {
      try {
        const response = await fetch(`https://*************${page}`, {
          method: "GET"
        });
        addResult(`Page ${page}: ${response.status} ${response.statusText}`);
      } catch (error) {
        addResult(`❌ Error testing page ${page}: ${error}`);
      }
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <>
      <Head>
        <title>OAuth Test - Dhruva Platform</title>
      </Head>
      
      <Box p={8} maxW="4xl" mx="auto">
        <VStack spacing={6} align="stretch">
          <Heading as="h1" size="xl" textAlign="center">
            OAuth Integration Test
          </Heading>
          
          <Alert status="info">
            <AlertIcon />
            This page helps debug the OAuth integration. Use the buttons below to test different aspects of the OAuth flow.
          </Alert>
          
          <VStack spacing={4}>
            <Button 
              colorScheme="blue" 
              onClick={testOAuthEndpoint}
              size="lg"
              w="full"
            >
              Test OAuth Endpoint
            </Button>
            
            <Button 
              colorScheme="green" 
              onClick={testFrontendPages}
              size="lg"
              w="full"
            >
              Test Frontend Pages
            </Button>
            
            <Button 
              colorScheme="red" 
              onClick={testDirectOAuth}
              size="lg"
              w="full"
            >
              Test Direct OAuth Flow
            </Button>
            
            <Button 
              variant="outline" 
              onClick={clearResults}
              size="sm"
            >
              Clear Results
            </Button>
          </VStack>
          
          {testResults.length > 0 && (
            <Box>
              <Heading as="h3" size="md" mb={4}>
                Test Results:
              </Heading>
              <Box 
                bg="gray.50" 
                p={4} 
                borderRadius="md" 
                maxH="400px" 
                overflowY="auto"
              >
                {testResults.map((result, index) => (
                  <Code key={index} display="block" mb={2} p={2} bg="white">
                    {result}
                  </Code>
                ))}
              </Box>
            </Box>
          )}
          
          <Alert status="warning">
            <AlertIcon />
            <VStack align="start" spacing={2}>
              <Text fontWeight="bold">Expected OAuth Flow:</Text>
              <Text fontSize="sm">
                1. Click "Test Direct OAuth Flow" → Should redirect to Google consent screen
              </Text>
              <Text fontSize="sm">
                2. After Google consent → Should redirect to /dhruva/oauth/success with token
              </Text>
              <Text fontSize="sm">
                3. Success page processes token → Redirects to platform
              </Text>
            </VStack>
          </Alert>
        </VStack>
      </Box>
    </>
  );
}
