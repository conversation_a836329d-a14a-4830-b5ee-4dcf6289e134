import { Box, Container, Heading, Text, VStack, Link } from "@chakra-ui/react";
import Head from "next/head";

export default function PrivacyPolicy() {
  return (
    <>
      <Head>
        <title>Privacy Policy - Dhruva Platform</title>
        <meta name="description" content="Privacy Policy for Dhruva Platform" />
      </Head>
      
      <Container maxW="4xl" py={8}>
        <VStack spacing={6} align="start">
          <Heading as="h1" size="xl" color="blue.600">
            Privacy Policy
          </Heading>
          
          <Text fontSize="sm" color="gray.600">
            Last updated: {new Date().toLocaleDateString()}
          </Text>

          <Box>
            <Heading as="h2" size="lg" mb={4}>
              1. Information We Collect
            </Heading>
            <Text mb={4}>
              When you use Google OAuth to sign in to Dhruva Platform, we collect:
            </Text>
            <VStack align="start" spacing={2} pl={4}>
              <Text>• Your Google account email address</Text>
              <Text>• Your name and profile information</Text>
              <Text>• Basic account information necessary for authentication</Text>
            </VStack>
          </Box>

          <Box>
            <Heading as="h2" size="lg" mb={4}>
              2. How We Use Your Information
            </Heading>
            <Text mb={4}>
              We use your information to:
            </Text>
            <VStack align="start" spacing={2} pl={4}>
              <Text>• Authenticate your access to the platform</Text>
              <Text>• Create and manage your user account</Text>
              <Text>• Provide platform services and features</Text>
              <Text>• Generate API keys for platform access</Text>
            </VStack>
          </Box>

          <Box>
            <Heading as="h2" size="lg" mb={4}>
              3. Data Storage and Security
            </Heading>
            <Text mb={4}>
              Your data is stored securely and we implement appropriate security measures to protect your information.
              We do not share your personal information with third parties except as necessary to provide our services.
            </Text>
          </Box>

          <Box>
            <Heading as="h2" size="lg" mb={4}>
              4. Google OAuth Integration
            </Heading>
            <Text mb={4}>
              Our Google OAuth integration follows Google's security and privacy guidelines. We only request the minimum
              necessary permissions (openid, email, profile) to provide authentication services.
            </Text>
          </Box>

          <Box>
            <Heading as="h2" size="lg" mb={4}>
              5. Your Rights
            </Heading>
            <Text mb={4}>
              You have the right to:
            </Text>
            <VStack align="start" spacing={2} pl={4}>
              <Text>• Access your personal information</Text>
              <Text>• Request deletion of your account</Text>
              <Text>• Revoke OAuth permissions at any time</Text>
              <Text>• Contact us with privacy concerns</Text>
            </VStack>
          </Box>

          <Box>
            <Heading as="h2" size="lg" mb={4}>
              6. Contact Information
            </Heading>
            <Text>
              If you have any questions about this Privacy Policy, please contact us at:{" "}
              <Link href="mailto:<EMAIL>" color="blue.500">
                <EMAIL>
              </Link>
            </Text>
          </Box>

          <Box>
            <Heading as="h2" size="lg" mb={4}>
              7. Changes to This Policy
            </Heading>
            <Text>
              We may update this Privacy Policy from time to time. We will notify you of any changes by posting
              the new Privacy Policy on this page.
            </Text>
          </Box>
        </VStack>
      </Container>
    </>
  );
}
