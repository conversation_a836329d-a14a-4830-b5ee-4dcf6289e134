import { Box, Container, Heading, Text, VStack, Link } from "@chakra-ui/react";
import Head from "next/head";

export default function TermsOfService() {
  return (
    <>
      <Head>
        <title>Terms of Service - Dhruva Platform</title>
        <meta name="description" content="Terms of Service for Dhruva Platform" />
      </Head>
      
      <Container maxW="4xl" py={8}>
        <VStack spacing={6} align="start">
          <Heading as="h1" size="xl" color="blue.600">
            Terms of Service
          </Heading>
          
          <Text fontSize="sm" color="gray.600">
            Last updated: {new Date().toLocaleDateString()}
          </Text>

          <Box>
            <Heading as="h2" size="lg" mb={4}>
              1. Acceptance of Terms
            </Heading>
            <Text mb={4}>
              By accessing and using Dhruva Platform, you accept and agree to be bound by the terms and provision of this agreement.
            </Text>
          </Box>

          <Box>
            <Heading as="h2" size="lg" mb={4}>
              2. Use License
            </Heading>
            <Text mb={4}>
              Permission is granted to temporarily access Dhruva Platform for personal, non-commercial transitory viewing only.
              This is the grant of a license, not a transfer of title, and under this license you may not:
            </Text>
            <VStack align="start" spacing={2} pl={4}>
              <Text>• Modify or copy the materials</Text>
              <Text>• Use the materials for any commercial purpose or for any public display</Text>
              <Text>• Attempt to reverse engineer any software contained on the platform</Text>
              <Text>• Remove any copyright or other proprietary notations from the materials</Text>
            </VStack>
          </Box>

          <Box>
            <Heading as="h2" size="lg" mb={4}>
              3. Google OAuth Authentication
            </Heading>
            <Text mb={4}>
              By using Google OAuth to sign in, you agree that:
            </Text>
            <VStack align="start" spacing={2} pl={4}>
              <Text>• You have a valid Google account</Text>
              <Text>• You consent to sharing your basic profile information</Text>
              <Text>• You understand that we will store your email and profile data</Text>
              <Text>• You can revoke access at any time through your Google account settings</Text>
            </VStack>
          </Box>

          <Box>
            <Heading as="h2" size="lg" mb={4}>
              4. API Usage
            </Heading>
            <Text mb={4}>
              When using our API services:
            </Text>
            <VStack align="start" spacing={2} pl={4}>
              <Text>• You are responsible for securing your API keys</Text>
              <Text>• You agree not to exceed reasonable usage limits</Text>
              <Text>• You will not use the API for illegal or harmful purposes</Text>
              <Text>• We reserve the right to suspend access for misuse</Text>
            </VStack>
          </Box>

          <Box>
            <Heading as="h2" size="lg" mb={4}>
              5. Disclaimer
            </Heading>
            <Text mb={4}>
              The materials on Dhruva Platform are provided on an 'as is' basis. Dhruva Platform makes no warranties,
              expressed or implied, and hereby disclaims and negates all other warranties including without limitation,
              implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement
              of intellectual property or other violation of rights.
            </Text>
          </Box>

          <Box>
            <Heading as="h2" size="lg" mb={4}>
              6. Limitations
            </Heading>
            <Text mb={4}>
              In no event shall Dhruva Platform or its suppliers be liable for any damages (including, without limitation,
              damages for loss of data or profit, or due to business interruption) arising out of the use or inability to
              use the materials on Dhruva Platform, even if Dhruva Platform or an authorized representative has been
              notified orally or in writing of the possibility of such damage.
            </Text>
          </Box>

          <Box>
            <Heading as="h2" size="lg" mb={4}>
              7. Accuracy of Materials
            </Heading>
            <Text mb={4}>
              The materials appearing on Dhruva Platform could include technical, typographical, or photographic errors.
              Dhruva Platform does not warrant that any of the materials on its platform are accurate, complete, or current.
            </Text>
          </Box>

          <Box>
            <Heading as="h2" size="lg" mb={4}>
              8. Links
            </Heading>
            <Text mb={4}>
              Dhruva Platform has not reviewed all of the sites linked to our platform and is not responsible for the
              contents of any such linked site. The inclusion of any link does not imply endorsement by Dhruva Platform
              of the site.
            </Text>
          </Box>

          <Box>
            <Heading as="h2" size="lg" mb={4}>
              9. Modifications
            </Heading>
            <Text mb={4}>
              Dhruva Platform may revise these terms of service at any time without notice. By using this platform,
              you are agreeing to be bound by the then current version of these terms of service.
            </Text>
          </Box>

          <Box>
            <Heading as="h2" size="lg" mb={4}>
              10. Contact Information
            </Heading>
            <Text>
              If you have any questions about these Terms of Service, please contact us at:{" "}
              <Link href="mailto:<EMAIL>" color="blue.500">
                <EMAIL>
              </Link>
            </Text>
          </Box>
        </VStack>
      </Container>
    </>
  );
}
