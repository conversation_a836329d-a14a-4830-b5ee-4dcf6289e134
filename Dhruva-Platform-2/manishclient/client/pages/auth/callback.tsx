import { useEffect } from "react";
import { useRouter } from "next/router";

export default function OAuthCallback() {
  const router = useRouter();

  useEffect(() => {
    if (router.isReady) {
      const { access_token, refresh_token, error } = router.query;

      if (error) {
        console.error("OAuth Error:", error);
        // Optionally show an error message to the user
        router.push("/"); // Redirect to login page on error
        return;
      }

      if (access_token && refresh_token) {
        localStorage.setItem("access_token", access_token as string);
        localStorage.setItem("refresh_token", refresh_token as string);

        const currentPage = localStorage.getItem("current_page");
        if (currentPage) {
          window.location.href = currentPage; // Full page redirect with base path
        } else {
          router.push("/testing-ground"); // Default redirect
        }
      } else {
        // If no tokens or error, redirect to login
        router.push("/");
      }
    }
  }, [router]);

  return (
    <div>
      <p>Processing Google login...</p>
    </div>
  );
} 