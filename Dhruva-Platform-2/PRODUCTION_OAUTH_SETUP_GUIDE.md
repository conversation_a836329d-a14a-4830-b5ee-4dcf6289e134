# Production-Ready Google OAuth Setup Guide

## 🎯 **Overview**

This guide will help you configure the Dhruva Platform's Google OAuth integration for production use without test user restrictions, including advanced security features like RISC (Risk and Incident Sharing and Communications) integration.

## 📋 **Phase 1: Google Cloud Console Production Configuration**

### **Step 1: OAuth Consent Screen for Public Use**

#### **1.1 Access OAuth Consent Screen**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project
3. Navigate to **APIs & Services** → **OAuth consent screen**

#### **1.2 Configure for Public Use**
```
User Type: External ✅ (Required for public access)

App Information:
- App name: Dhruva Platform
- User support email: <EMAIL>
- App logo: Upload a 120x120px logo (optional but recommended)

App Domain:
- Application home page: https://*************/dhruva
- Application privacy policy link: https://*************/dhruva/privacy
- Application terms of service link: https://*************/dhruva/terms

Authorized domains:
- *************

Developer contact information:
- Email addresses: <EMAIL>
```

#### **1.3 Configure Scopes (Minimal Required)**
```
Scopes for Google APIs:
- ../auth/userinfo.email (See your primary Google Account email address)
- ../auth/userinfo.profile (See your personal info, including any personal info you've made publicly available)
- openid (Associate you with your personal info on Google)

Non-sensitive scopes: ✅ (No verification required)
```

#### **1.4 Publishing Status**
```
Publishing status: In production ✅

This allows any Google user to authenticate without being added as test users.
```

### **Step 2: App Verification (If Required)**

#### **2.1 Verification Requirements**
For public OAuth apps, Google may require verification if:
- You request sensitive or restricted scopes
- Your app has high usage volume
- Google's automated systems flag your app

#### **2.2 Our App Status**
```
✅ No verification required because:
- We only use non-sensitive scopes (email, profile, openid)
- These are standard authentication scopes
- No access to user data beyond basic profile
```

### **Step 3: Production Credentials Configuration**

#### **3.1 Update OAuth 2.0 Client**
```
Application type: Web application
Name: Dhruva Platform Production OAuth

Authorized JavaScript origins:
- https://*************

Authorized redirect URIs:
- https://*************/auth/oauth/google/callback
- https://*************/auth/oauth/google/risc (for RISC events)
```

## 📋 **Phase 2: Enhanced Security Configuration**

### **Step 1: Update Environment Variables**

#### **1.1 Production OAuth Settings**
```bash
# Production OAuth Configuration
GOOGLE_OAUTH_CLIENT_ID=235810295758-lhf7ekmbta2hjp0dd580lvm0301o1lue.apps.googleusercontent.com
GOOGLE_OAUTH_CLIENT_SECRET=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
GOOGLE_OAUTH_REDIRECT_URI=https://*************/auth/oauth/google/callback

# Production Security Settings
OAUTH_REQUIRE_HTTPS=true
OAUTH_SECURE_COOKIES=true
OAUTH_SAME_SITE_COOKIES=strict
OAUTH_STATE_EXPIRY_MINUTES=5
OAUTH_TOKEN_REFRESH_THRESHOLD_MINUTES=15

# RISC Configuration
RISC_WEBHOOK_URL=https://*************/auth/oauth/google/risc
RISC_WEBHOOK_SECRET=your-risc-webhook-secret-key-32-chars-min
RISC_ENABLE_LOGGING=true
RISC_AUTO_REVOKE_COMPROMISED=true
```

### **Step 2: Enhanced OAuth Scopes Validation**

#### **2.1 Minimal Scope Principle**
Our current scopes are already minimal and production-ready:
```
- openid: Required for OAuth 2.0 authentication
- email: Required for user identification
- profile: Required for user display name and avatar
```

#### **2.2 Scope Justification**
```
✅ openid: Essential for secure authentication
✅ email: Required for user account creation and identification
✅ profile: Enhances user experience with name and avatar
❌ No additional scopes requested (following minimal access principle)
```

## 📋 **Phase 3: RISC Integration Implementation**

### **Step 1: RISC Event Types**

#### **1.1 Supported Security Events**
```
- account-takeover: Account compromise detected
- suspicious-activity: Unusual account activity
- credential-compromise: Password or credentials compromised
- session-revocation: Session should be revoked
- account-disabled: Account has been disabled
```

### **Step 2: RISC Webhook Configuration**

#### **2.1 Google Security Center Setup**
1. Go to Google Cloud Console → Security → Security Center
2. Configure RISC webhook endpoint: `https://*************/auth/oauth/google/risc`
3. Set webhook secret for signature verification

### **Step 3: RISC Response Actions**

#### **3.1 Automated Security Responses**
```
account-takeover → Immediately revoke all user sessions + API keys
suspicious-activity → Log event + notify user + require re-authentication
credential-compromise → Revoke OAuth tokens + force password reset
session-revocation → Revoke specific session
account-disabled → Disable user account + revoke all access
```

## 📋 **Phase 4: Production Deployment Checklist**

### **Step 1: Pre-Deployment Verification**
- [ ] OAuth consent screen configured for public use
- [ ] App published (not in testing mode)
- [ ] Privacy policy and terms of service accessible
- [ ] Redirect URIs match production URLs exactly
- [ ] HTTPS enforced for all OAuth endpoints
- [ ] RISC webhook endpoint configured

### **Step 2: Security Validation**
- [ ] Minimal scopes implemented (openid, email, profile only)
- [ ] PKCE enabled for all OAuth flows
- [ ] State parameter validation implemented
- [ ] Token encryption at rest configured
- [ ] Secure cookie settings enabled
- [ ] RISC event handling implemented

### **Step 3: Monitoring and Logging**
- [ ] OAuth authentication metrics tracked
- [ ] RISC security events logged
- [ ] Failed authentication attempts monitored
- [ ] Token refresh patterns analyzed
- [ ] Suspicious activity detection enabled

## 🎯 **Expected Production Behavior**

### **Public OAuth Access**
```
✅ Any Google user can authenticate
✅ No test user restrictions
✅ Immediate access after consent
✅ Professional consent screen with app branding
✅ Secure token handling with encryption
```

### **Enhanced Security**
```
✅ RISC security event monitoring
✅ Automated threat response
✅ Minimal scope access
✅ Production-grade token security
✅ Comprehensive audit logging
```

### **User Experience**
```
✅ One-click Google authentication
✅ Professional app consent screen
✅ Immediate platform access
✅ Automatic account creation with API keys
✅ Seamless session management
```

## 🔧 **Implementation Timeline**

### **Immediate (Day 1)**
1. Update Google Cloud Console OAuth consent screen
2. Publish app for public use
3. Update production environment variables
4. Deploy enhanced security settings

### **Short-term (Week 1)**
1. Implement RISC webhook endpoint
2. Configure security event handling
3. Set up monitoring and alerting
4. Test complete production flow

### **Ongoing**
1. Monitor OAuth usage patterns
2. Analyze RISC security events
3. Optimize security responses
4. Maintain compliance with Google policies

## 📞 **Support and Maintenance**

### **Google OAuth Policies**
- Monitor Google's OAuth policy updates
- Ensure continued compliance with security requirements
- Respond to any verification requests from Google

### **Security Monitoring**
- Regular review of RISC security events
- Analysis of authentication patterns
- Proactive threat detection and response

This configuration will provide a production-ready, secure, and scalable Google OAuth integration that any Google user can access without restrictions.
